import type {
  AppLoginResultInfo,
  AppUserInfo,
  KnowledgeAssistantMemberInfo,
  TeamInfo
} from '@/services/types/loginMobileRes'
import type { Response } from '@/services/types/response'

import request from '@/utils/request'

import { getAppId } from '@/utils/constants'

export async function loginMobile(params: any): Promise<Response<AppLoginResultInfo>> {
  return request.post('/login/loginMobile', params)
}

export async function getUserInfo(): Promise<Response<AppUserInfo>> {
  return request.get('/user/getUserInfo', {
    params: {
      appid: getAppId()
    }
  })
}

export function repositoryGetInfo(params: any): Promise<Response<KnowledgeAssistantMemberInfo>> {
  return request.get('/repository/getInfo', {
    params
  })
}

export async function getTeamCoinBalance(params: any): Promise<Response<any>> {
  return request.get(`/team/${params.teamId}/coin/balance`)
}

export const generateContent = async (params: any): Promise<any> => {
  return request.post('/submissionEdit/doAiAction', params)
}

export const generateSchematic = async (params: any): Promise<any> => {
  return request.post('/submissionEdit/schematic/create', params)
}

export const changeSchematic = async (params: any): Promise<any> => {
  return request.post('/submissionEdit/schematic/change', params)
}
