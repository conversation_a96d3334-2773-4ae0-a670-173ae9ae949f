import type { Element, Text, Use } from '@svgdotjs/svg.js'
import type { SvgObject } from '@/services/types/svg'

import { G, Matrix, Path, Shape, SVG, Text as SvgText } from '@svgdotjs/svg.js'

import * as topath from '@svgdotjs/svg.topath.js'

interface AlignParams {
  horizontal: 'CENTER' | 'LEFT' | 'RIGHT'
  vertical: 'CENTER' | 'TOP' | 'BOTTOM'
}

/**
 * SVG变换矩阵接口
 */
interface TransformMatrix {
  a: number // 水平缩放
  b: number // 水平倾斜
  c: number // 垂直倾斜
  d: number // 垂直缩放
  e: number // 水平移动
  f: number // 垂直移动
}

export function clearTransform(element: Element) {
  // 获取当前transform矩阵
  const matrix = parseTransform(element)
  // if (matrix.a === 1 && matrix.b === 0 && matrix.c === 0 && matrix.d === 1 && matrix.e === 0 && matrix.f === 0)
  //   return element

  // 创建一个新的transform矩阵，保留旋转和缩放，但移除平移
  const cleanedTransform = {
    a: matrix.a, // 保留水平缩放
    b: matrix.b, // 保留垂直剪切
    c: matrix.c, // 保留水平剪切
    d: matrix.d, // 保留垂直缩放
    e: 2, // 清除x方向平移
    f: 2 // 清除y方向平移
  }

  // 应用清理后的transform矩阵
  element.transform(cleanedTransform)

  return element
}

export function fixD(element: Element, width: number, height: number) {
  // 获取当前transform矩阵
  const matrix = parseTransform(element)

  // 修复矩阵
  const fixedMatrix = fixMatrix(matrix, width + 4, height + 4, 2)
  // 应用清理后的transform矩阵
  element.transform(fixedMatrix)
  return fixedMatrix
}

/**
 * 修复SVG变换矩阵的位移值
 * @param matrix 原始变换矩阵对象
 * @param totalWidth 元素总宽度（包含边距）
 * @param totalHeight 元素总高度（包含边距）
 * @param padding 边距大小
 * @returns 修复后的变换矩阵对象
 */
function fixMatrix(
  matrix: TransformMatrix,
  totalWidth: number,
  totalHeight: number,
  padding: number
): TransformMatrix {
  // 复制一个新矩阵以避免修改原始数据
  const newMatrix: TransformMatrix = { ...matrix }

  // 使用小量值(epsilon)比较来处理浮点数
  const epsilon = 0.0001

  if (
    Math.abs(matrix.a) < epsilon &&
    Math.abs(matrix.b - 1) < epsilon &&
    Math.abs(matrix.c + 1) < epsilon &&
    Math.abs(matrix.d) < epsilon
  ) {
    // 90度顺时针旋转：matrix(0,1,-1,0,e,f)
    newMatrix.e = totalWidth - padding
    newMatrix.f = padding
  } else if (
    Math.abs(matrix.a + 1) < epsilon &&
    Math.abs(matrix.b) < epsilon &&
    Math.abs(matrix.c) < epsilon &&
    Math.abs(matrix.d - 1) < epsilon
  ) {
    // 水平翻转：matrix(-1,0,0,1,e,f)
    newMatrix.e = totalWidth - padding
    newMatrix.f = padding
  } else if (
    Math.abs(matrix.a - 1) < epsilon &&
    Math.abs(matrix.b) < epsilon &&
    Math.abs(matrix.c) < epsilon &&
    Math.abs(matrix.d + 1) < epsilon
  ) {
    // 垂直翻转：matrix(1,0,0,-1,e,f)
    newMatrix.e = padding
    newMatrix.f = totalHeight - padding
  } else if (
    Math.abs(matrix.a) < epsilon &&
    Math.abs(matrix.b + 1) < epsilon &&
    Math.abs(matrix.c - 1) < epsilon &&
    Math.abs(matrix.d) < epsilon
  ) {
    // 270度顺时针旋转：matrix(0,-1,1,0,e,f)
    newMatrix.e = padding
    newMatrix.f = totalHeight - padding
  } else if (
    Math.abs(matrix.a + 1) < epsilon &&
    Math.abs(matrix.b) < epsilon &&
    Math.abs(matrix.c) < epsilon &&
    Math.abs(matrix.d + 1) < epsilon
  ) {
    // 180度旋转：matrix(-1,0,0,-1,e,f)
    newMatrix.e = totalWidth - padding
    newMatrix.f = totalHeight - padding
  }

  return newMatrix
}

/**
 * 解析SVG元素的transform属性为矩阵对象
 * @param element - 包含transform属性的元素
 * @returns 表示变换的矩阵对象
 */
export function parseTransform(element: Element): TransformMatrix {
  const transform = element.attr('transform')
  if (!transform) {
    return {
      a: 1,
      b: 0,
      c: 0,
      d: 1,
      e: 0,
      f: 0
    }
  }

  try {
    // 匹配matrix和translate变换
    const matrixPattern = /matrix\(([^)]+)\)/
    const translatePattern = /translate\(([^)]+)\)/

    // 默认为单位矩阵
    let matrix: TransformMatrix = {
      a: 1,
      b: 0,
      c: 0,
      d: 1,
      e: 0,
      f: 0
    }

    // 处理matrix变换
    const matrixMatch = transform.match(matrixPattern)
    if (matrixMatch) {
      const values = matrixMatch[1].split(/[\s,]+/).map(Number)
      if (values.length === 6) {
        matrix = {
          a: values[0],
          b: values[1],
          c: values[2],
          d: values[3],
          e: values[4],
          f: values[5]
        }
      } else {
        console.warn(`变换中的矩阵值无效: "${transform}"`)
      }
    }
    // 处理translate变换
    else {
      const translateMatch = transform.match(translatePattern)
      if (translateMatch) {
        const values = translateMatch[1].split(/[\s,]+/).map(Number)
        matrix = {
          a: 1,
          b: 0,
          c: 0,
          d: 1,
          e: values[0] || 0,
          f: values.length > 1 ? values[1] : 0
        }
      } else {
        console.warn(`不支持或复杂的变换字符串格式无法解析: "${transform}"。使用单位矩阵。`)
      }
    }

    return matrix
  } catch (error) {
    console.error(`解析变换字符串出错: "${transform}"`, error)
    return {
      a: 1,
      b: 0,
      c: 0,
      d: 1,
      e: 0,
      f: 0
    }
  }
}

/**
 * 合并两个变换矩阵
 * @param m1 - 第一个矩阵
 * @param m2 - 第二个矩阵
 * @returns 合并后的矩阵
 */
export function combineMatrices(m1: TransformMatrix, m2: TransformMatrix): TransformMatrix {
  return {
    a: m1.a * m2.a + m1.c * m2.b,
    b: m1.b * m2.a + m1.d * m2.b,
    c: m1.a * m2.c + m1.c * m2.d,
    d: m1.b * m2.c + m1.d * m2.d,
    e: m1.a * m2.e + m1.c * m2.f + m1.e,
    f: m1.b * m2.e + m1.d * m2.f + m1.f
  }
}

/**
 * 将元素的属性转移到use元素上，保留path的d属性
 * 如果元素是Shape类型，会转换为Path
 * @param element 原始SVG元素
 * @param use 目标use元素
 */
export function transferAttributes(element: Element, use: Use): Element {
  // 如果元素是Shape类型，将其转换为Path
  if (element instanceof Shape && !(element instanceof Path)) {
    // 使用toPath方法将Shape转换为Path
    // 参数false表示不替换原始元素
    copyAttributes(element, use)
    // const pathElement = element.toPath(false)
    return element
  } else {
    // 对于其他类型的元素，直接复制属性
    copyAttributes(element, use)
    return element
  }
}

/**
 * 复制元素的属性到use元素
 * @param element 源元素
 * @param use 目标use元素
 */
function copyAttributes(element: Element, target: Element): void {
  // 获取原始元素的所有属性
  const attrs = element.attr()

  // 遍历所有属性并转移到use元素
  for (const [key, value] of Object.entries(attrs)) {
    if (key.startsWith('stroke') || key.startsWith('fill') || key.startsWith('data')) {
      // 转移属性到use元素
      target.attr(key, value)

      element.attr(key, null)
    }
  }
}

// 随机字符串函数
export function randomString(length: number) {
  let result = ''
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  const charactersLength = characters.length
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength))
  }
  return result
}

export class SvgProcessor {
  private svg: Element | null = null
  private svgList: SvgObject[] = []
  private svgWidth: number = 0
  private svgHeight: number = 0

  constructor(svgContent: string) {
    this.svg = SVG(svgContent)

    this.svgWidth = Number.parseInt(this.svg.width().toString()) // 获取 SVG 的宽度
    this.svgHeight = Number.parseInt(this.svg.height().toString()) // 获取 SVG 的高度
    this.svg.children().forEach((element) => {
      // if (element.type === 'defs') {
      //   defsBlock.value = element.children()
      // }
      if (element.type === 'g') {
        this.parseChildren(element, new Matrix(0, 0, 0, 0, 0, 0))
      }
    })
  }

  private parseChildren(svg: Element, parentMatrix: Matrix) {
    const children = svg.children()
    const matrix = svg.matrix()
    matrix.e += parentMatrix.e
    matrix.f += parentMatrix.f
    const cloneSvg = svg.clone(true, false)

    children.forEach((element) => {
      // if (element.id().toString().startsWith('bt-cc')) {
      //   return
      // }

      // rect 特殊处理
      if (element.type === 'rect' && element.id().toString().startsWith('tx-cb-title')) {
        // 获取rect的尺寸
        const rectWidth = Number.parseInt(element.attr('width') || '0')
        const rectHeight = Number.parseInt(element.attr('height') || '0')

        // 创建路径数据
        const pathData = `M0 0 L${rectWidth} 0 L${rectWidth} ${rectHeight} L0 ${rectHeight} L0 0 Z`

        // 创建新的path元素
        const newPath = new Path()
        newPath.id(element.id())
        newPath.attr('d', pathData)

        // 复制原始元素的属性
        const fillAttr = element.attr('fill')
        if (fillAttr) {
          newPath.attr('fill', fillAttr)
        }

        const transformAttr = element.attr('transform')
        if (transformAttr) {
          newPath.attr('transform', transformAttr)
        }

        // 替换原始元素
        element = newPath
      }

      if (element.type === 'g') {
        this.parseChildren(element, matrix)
      } else {
        // matrix.a = cloneSvg.width()
        // matrix.b = cloneSvg.height()
        // console.log('element', element.type, element.id(), cloneSvg.type, cloneSvg.id(), cloneSvg.width(), cloneSvg.height())
        this.addToList(element, matrix, cloneSvg)
      }
    })
  }

  private addToList(element: Element, parentMatrix: Matrix, parent: Element) {
    // 跳过id为bt-cc-add开头的rect元素
    const elementId = element.id().toString()
    if (
      element.type === 'rect' &&
      (elementId.startsWith('bt-cc') || elementId.endsWith('bt-rc') || elementId.endsWith('bt-lc'))
    ) {
      return // 跳过这个元素，不添加到结果中
    }

    let id = elementId
    let width = Number.parseInt(element.width().toString())
    let height = Number.parseInt(element.height().toString())

    const matrix = parseTransform(element)
    element.id(`${id}_${randomString(10)}`)
    matrix.e += parentMatrix.e
    matrix.f += parentMatrix.f

    let hasPos = false
    const pos = element.attr('data-position')
    if (pos) {
      hasPos = true
      pos.split(';').forEach((item: string) => {
        const [key, value] = item.split(':')
        if (key === 'w') {
          width = Number.parseInt(value || '0')
        }
        if (key === 'h') {
          height = Number.parseInt(value || '0')
        }
      })
    }
    //   is_text_in_g：text 在 g 中，且 g 是以 tx - 开头
    const is_text_in_g =
      element.type === 'text' && !id.startsWith('tx-') && parent && parent.id().startsWith('tx-')
    //   is_path：是 path，且 id 以 tx - 开头
    const is_path = element.type === 'path' && id.startsWith('tx-')
    //   has_tspan：text 元素包含 tspan 子元素
    const has_tspan = element.type === 'text' && element.find('tspan').length > 0

    // 适用于 text 在 g里, 同级有一个path的情况
    if (is_text_in_g || is_path) {
      // 处理含有tspan的text元素
      if (has_tspan && element.type === 'text') {
        // 获取第一个tspan元素
        const tspans = element.find('tspan')
        if (tspans.length > 0) {
          const firstTspan = tspans[0] as Element

          // 获取tspan的x和y属性
          const tspanX = firstTspan.attr('x')
          const tspanY = firstTspan.attr('y')

          // 确保值是数字，并保存为临时变量
          let finalX = element.attr('x')
          let finalY = element.attr('y')

          if (tspanX !== undefined && tspanX !== null) {
            finalX = tspanX
          }

          if (tspanY !== undefined && tspanY !== null) {
            finalY = tspanY
          }

          // 将tspan的内容设置为text的内容
          const tspanContent = firstTspan.node.textContent
          if (tspanContent && !id.endsWith('number')) {
            ;(element as Text).text(tspanContent)
          }

          // 保存坐标值，以便稍后应用（在对齐计算后）
          element.remember('originalX', finalX)
          element.remember('originalY', finalY)
        }
      }

      if (is_text_in_g) {
        // id = element.parent(G)!.id() || id
        const group = element.parent(G)!
        id = group.id() || id
        if (!hasPos) {
          width = Number.parseInt(parent.width().toString())
          height = Number.parseInt(parent.height().toString())
        }
        const siblings = group.children()
        for (const sibling of siblings) {
          if (sibling.type === 'path') {
            const pathTransform = sibling.attr('transform')
            if (pathTransform) {
              element.attr('transform', pathTransform)
              const pathMatrix = parseTransform(sibling)
              matrix.e = pathMatrix.e
              matrix.f = pathMatrix.f
            }
            break
          }
        }
      }

      // // 如果元素是rect，则计算其坐标 特殊处理
      if (element.type === 'rect' && id.startsWith('tx-')) {
        const new_element = new Path()
        new_element.id(element.id())
        copyAttributes(element, new_element)

        // 获取rect的位置和尺寸
        const x = Number.parseFloat(element.attr('x') || '0')
        const y = Number.parseFloat(element.attr('y') || '0')
        const rectWidth = Number.parseFloat(element.attr('width') || '0')
        const rectHeight = Number.parseFloat(element.attr('height') || '0')

        if (
          !Number.isNaN(x) &&
          !Number.isNaN(y) &&
          !Number.isNaN(rectWidth) &&
          !Number.isNaN(rectHeight)
        ) {
          // 保存rect的坐标信息
          width = rectWidth
          height = rectHeight

          // 保存坐标信息到元素的自定义数据中
          new_element.data('rect-x', x)
          new_element.data('rect-y', y)
          new_element.data('rect-width', width)
          new_element.data('rect-height', height)

          // 检查元素是否已经有变换
          const elementTransform = element.attr('transform')
          if (elementTransform) {
            // 如果元素已经有变换
            if (elementTransform.includes('translate')) {
              // 处理 translate 变换
              const translateMatch = elementTransform.match(/translate\(([^,\s)]+)(?:,([^)]+))?\)/)
              if (translateMatch) {
                const tx = Number.parseFloat(translateMatch[1] || '0')
                const ty = Number.parseFloat(translateMatch[2] || '0')
                // 使用原始的 translate 值
                matrix.e = tx
                matrix.f = ty
                // 将原始变换复制到新元素
                new_element.attr('transform', elementTransform)
              }
            } else if (elementTransform.includes('matrix')) {
              // 处理 matrix 变换
              const matrixMatch = elementTransform.match(/matrix\(([^)]+)\)/)
              if (matrixMatch) {
                const values = matrixMatch[1].split(/[\s,]+/).map(Number)
                if (values.length === 6) {
                  // 使用原始的 matrix 中的 e, f 值（位移部分）
                  matrix.e = values[4]
                  matrix.f = values[5]
                  // 将原始变换复制到新元素
                  new_element.attr('transform', elementTransform)
                }
              }
            } else {
              // 其他类型的变换，使用rect的x,y坐标
              matrix.e = x
              matrix.f = y
            }
          } else {
            // 没有任何变换，使用rect的x,y坐标
            matrix.e = x
            matrix.f = y
          }
        }

        element = new_element
      }

      // 如果元素是path，则计算其坐标 特殊处理
      // 如果元素是path，则计算其坐标 特殊处理
      if (is_path) {
        const new_element = new SvgText()
        new_element.id(element.id())
        copyAttributes(element, new_element)

        // 获取原始路径的 d 属性并存储它
        const pathData = element.attr('d')
        if (pathData) {
          // 将 d 属性保存到元素的自定义数据中，以便后续使用
          new_element.data('original-path-d', pathData)

          // 计算路径的边界框和坐标
          let minX = Infinity
          let minY = Infinity
          let maxX = -Infinity
          let maxY = -Infinity

          try {
            // 直接获取所有的数字
            const matches = pathData.match(/-?\d+(\.\d+)?/g)
            if (matches) {
              // 解析每个坐标点
              for (let i = 0; i < matches.length - 1; i += 2) {
                const xStr = matches[i]
                const yStr = matches[i + 1]
                if (xStr && yStr) {
                  const x = Number.parseFloat(xStr)
                  const y = Number.parseFloat(yStr)

                  if (!Number.isNaN(x) && !Number.isNaN(y)) {
                    // 更新边界值
                    minX = Math.min(minX, x)
                    minY = Math.min(minY, y)
                    maxX = Math.max(maxX, x)
                    maxY = Math.max(maxY, y)
                  }
                }
              }

              // 如果找到有效的边界框
              if (minX !== Infinity && minY !== Infinity) {
                // 计算宽度和高度
                width = maxX - minX
                height = maxY - minY

                // 保存坐标信息
                new_element.data('path-x', minX)
                new_element.data('path-y', minY)
                new_element.data('path-width', width)
                new_element.data('path-height', height)

                // 检查元素是否已经有 translate 变换
                const elementTransform = element.attr('transform')
                if (elementTransform) {
                  // 如果元素已经有变换
                  if (elementTransform.includes('translate')) {
                    // 处理 translate 变换
                    const translateMatch = elementTransform.match(
                      /translate\(([^,\s)]+)(?:,([^)]+))?\)/
                    )
                    if (translateMatch) {
                      const tx = Number.parseFloat(translateMatch[1] || '0')
                      const ty = Number.parseFloat(translateMatch[2] || '0')
                      // 使用原始的 translate 值
                      matrix.e = tx
                      matrix.f = ty
                      // 将原始变换复制到新元素
                      new_element.attr('transform', elementTransform)
                    }
                  } else if (elementTransform.includes('matrix')) {
                    // 处理 matrix 变换
                    const matrixMatch = elementTransform.match(/matrix\(([^)]+)\)/)
                    if (matrixMatch) {
                      const values = matrixMatch[1].split(/[\s,]+/).map(Number)
                      if (values.length === 6) {
                        // 使用原始的 matrix 中的 e, f 值（位移部分）
                        matrix.e = values[4]
                        matrix.f = values[5]
                        // 将原始变换复制到新元素
                        new_element.attr('transform', elementTransform)
                      }
                    }
                  } else {
                    // 其他类型的变换，目前不处理，使用计算出的边界框值
                    matrix.e = minX
                    matrix.f = minY
                  }
                } else {
                  // 没有任何变换，使用计算出的边界框值
                  matrix.e = minX
                  matrix.f = minY
                }
              }
            }
          } catch (error) {
            console.error('解析路径数据时出错:', error)
          }
        }

        element = new_element
      }

      // 原有的text处理逻辑
      let dataAlign: string = element.attr('data-align')
      if (!dataAlign) {
        dataAlign = 'horizontal:CENTER;vertical:BOTTOM'
      }
      // 解析对齐参数
      const alignParams: AlignParams = dataAlign.split(';').reduce<AlignParams>(
        (acc, param) => {
          const [key, value] = param.split(':')
          if (key === 'horizontal') {
            acc.horizontal = value as 'CENTER' | 'LEFT' | 'RIGHT'
          } else if (key === 'vertical') {
            acc.vertical = value as 'CENTER' | 'TOP' | 'BOTTOM'
          }
          return acc
        },
        { horizontal: 'CENTER', vertical: 'BOTTOM' }
      )

      // console.log('alignParams', alignParams)
      // 根据水平对齐方式设置x坐标和text-anchor
      let textAnchor = 'start' // 默认左对齐
      let textX = 0

      if (alignParams.horizontal === 'CENTER') {
        textAnchor = 'middle'
        textX = 0 + width / 2
      } else if (alignParams.horizontal === 'LEFT') {
        textAnchor = 'start'
        textX = 0
      } else if (alignParams.horizontal === 'RIGHT') {
        textAnchor = 'end'
        textX = 0 + width
      }

      // 根据垂直对齐方式设置y坐标和dominant-baseline
      let dominantBaseline = 'hanging' // 默认顶部对齐
      let textY = 0

      if (alignParams.vertical === 'CENTER') {
        dominantBaseline = 'middle'
        textY = 0 + height / 2
      } else if (alignParams.vertical === 'TOP') {
        dominantBaseline = 'hanging' // 应该使用hanging而不是auto
        textY = 0
      } else if (alignParams.vertical === 'BOTTOM') {
        dominantBaseline = 'ideographic' // 应该使用alphabetic或ideographic
        textY = 0 + height
      }
      element.attr('text-anchor', textAnchor)
      element.attr('dominant-baseline', dominantBaseline)
      element.attr('alignment-baseline', dominantBaseline)
      element.attr('x', textX)
      element.attr('y', textY)
      // console.log('text', id, textX, textY)

      if (!id.endsWith('number')) {
        ;(element as Text).text(id)
      }

      // 在设置对齐后应用保存的 tspan 坐标值
      if (has_tspan && element.type === 'text') {
        const savedX = element.remember('originalX')
        const savedY = element.remember('originalY')

        if (savedX !== undefined) {
          element.attr('x', savedX)
        }

        if (savedY !== undefined) {
          element.attr('y', savedY)
        }
      }
      // console.log('text', element.id(), parent.id(), width, height)
    }

    clearTransform(element)

    const change = fixD(element, width, height)

    const draw = SVG()
    const defs = draw.defs()

    const use = draw.use(element)
    transferAttributes(element, use)
    defs.add(element)

    this.svgList.push({
      x: matrix.e - change.e + 2,
      y: matrix.f - change.f + 2,
      width: width + 4,
      height: height + 4,
      defs,
      uses: [use],
      shadows: []
    })
  }

  // 获取处理结果
  public getResult() {
    // 在返回结果之前处理所有元素的颜色，去除透明度
    this.processAllColors()

    return {
      svg: this.svg,
      list: this.svgList,
      svgWidth: this.svgWidth,
      svgHeight: this.svgHeight
    }
  }

  private processAllColors() {
    console.log('🔍 开始处理所有颜色，svgList长度:', this.svgList.length)

    // 处理svgList中的所有元素
    this.svgList.forEach((svgObject, index) => {
      console.log(`📦 处理第${index}个svgObject，uses数量:`, svgObject.uses.length)

      // 首先直接检查所有use元素的fill属性（优先处理）
      svgObject.uses.forEach((use: any, useIndex) => {
        const fillValue = use.attr('fill')
        const fillOpacity = use.attr('fill-opacity')
        const href = use.attr('href')

        console.log(`🔎 检查use[${useIndex}]:`, {
          href,
          fillValue,
          fillOpacity,
          type: typeof fillValue
        })

        // 检查是否是 ff00001a 颜色（支持多种格式）
        const isTargetColor = this.isFF00001AColor(fillValue)

        if (isTargetColor) {
          console.log('✅ 找到ff00001a颜色，设置为透明:', fillValue)
          // 尝试多种透明设置方式
          use.attr('fill', 'none')
          use.attr('fill-opacity', 0)
          use.attr('opacity', 0)
          // 也可以尝试完全移除fill属性
          // use.attr('fill', null)
        }
      })

      if (svgObject.defs) {
        // 处理defs中的所有元素
        svgObject.defs.children().forEach((child: any) => {
          // 通过text的id，查找use的href，如果能匹配就修改该use的fill
          if (child.type === 'text') {
            const textId = child.id()
            // 查找svgObject.uses中href引用了这个text的use元素
            svgObject.uses.forEach((use: any) => {
              const href = use.attr('href')
              if (href && href === `#${textId}`) {
                const fillValue = use.attr('fill')
                const fillOpacity = use.attr('fill-opacity')

                // 检查是否是 ff00001a 颜色
                const isTargetColor = this.isFF00001AColor(fillValue)

                if (isTargetColor) {
                  console.log('✅ 通过text关联找到ff00001a颜色，设置为透明:', textId, fillValue)
                  use.attr('fill', 'none')
                  use.attr('fill-opacity', 0)
                  use.attr('opacity', 0)
                }
                // 处理其他需要修改的颜色
                else if (fillOpacity < 1 || fillValue === '#ff0000' || fillValue === '#ff0000a1') {
                  console.log('🔧 找到匹配的use元素，修改fill', textId, href, fillValue)
                  use.attr('fill', '#484848')
                  use.attr('fill-opacity', 1)
                  use.attr('opacity', 1)
                }
              }
            })
          } else {
            // 清除其他元素的fill属性
            child.attr('fill', null)
          }
        })
      }
    })

    console.log('✨ 颜色处理完成')
  }

  // 检查颜色是否是 ff00001a 的辅助方法
  private isFF00001AColor(fillValue: any): boolean {
    if (!fillValue || typeof fillValue !== 'string') {
      return false
    }

    const normalizedValue = fillValue.toLowerCase().replace('#', '')
    const targetColors = ['ff0000', 'ff00001a', '1ac6ff33', '33de7b1a', '33de7b']

    console.log('🎨 检查颜色:', fillValue, '标准化后:', normalizedValue)

    return targetColors.includes(normalizedValue)
  }
}
