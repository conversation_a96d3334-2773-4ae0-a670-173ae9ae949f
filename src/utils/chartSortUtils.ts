import type { Editor } from '@tiptap/core'

// 图表元素接口定义
export interface ChartElement {
  type: 'chart' | 'table' | 'image' | 'schematic'
  category: 'figure' | 'table' // 图 或 表
  position: number
  node: any
  originalLabel?: string
  newLabel?: string
  hasExistingTitle?: boolean
  titleNode?: any
}

// 图表排序结果接口
export interface ChartSortResult {
  success: boolean
  message: string
  processedCount: number
  figureCount: number
  tableCount: number
  elements: ChartElement[]
}

/**
 * 检查节点后是否有标题段落
 * @param editor TipTap编辑器实例
 * @param elementPos 元素位置
 * @param elementNode 元素节点
 * @returns 标题信息
 */
function checkForExistingTitle(editor: Editor, elementPos: number, elementNode: any) {
  try {
    const doc = editor.state.doc
    const elementSize = elementNode.nodeSize
    const nextPos = elementPos + elementSize

    // 检查元素后的下一个节点是否是段落
    if (nextPos < doc.content.size) {
      const nextNode = doc.nodeAt(nextPos)
      if (nextNode && nextNode.type.name === 'paragraph') {
        const textContent = nextNode.textContent.trim()
        // 跳过只有标点符号的段落，查找真正的标题
        if (textContent && textContent.length < 100 && !/^[。，、；：！？\s]*$/.test(textContent)) {
          return {
            hasTitle: true,
            titleNode: nextNode,
            titlePos: nextPos,
            titleText: textContent
          }
        }
      }
    }

    return { hasTitle: false }
  } catch (error) {
    console.warn('检查标题时出错:', error)
    return { hasTitle: false }
  }
}

/**
 * 识别编辑器中的所有图表和表格元素
 * @param editor TipTap编辑器实例
 * @returns 识别到的元素数组
 */
export function identifyChartsAndTables(editor: Editor): ChartElement[] {
  if (!editor || !editor.state || !editor.state.doc) {
    return []
  }

  const elements: ChartElement[] = []

  try {
    // 遍历编辑器文档的所有节点
    editor.state.doc.descendants((node: any, pos: number) => {
      const nodeType = node.type.name
      // 识别表格元素
      if (nodeType === 'table') {
        const titleInfo = checkForExistingTitle(editor, pos, node)
        elements.push({
          type: 'table',
          category: 'table',
          position: pos,
          node: node,
          hasExistingTitle: titleInfo.hasTitle,
          titleNode: titleInfo.titleNode
        })
      }
      // 识别图表元素
      else if (nodeType === 'chart') {
        const titleInfo = checkForExistingTitle(editor, pos, node)
        elements.push({
          type: 'chart',
          category: 'figure',
          position: pos,
          node: node,
          hasExistingTitle: titleInfo.hasTitle,
          titleNode: titleInfo.titleNode
        })
      }
      // 识别图片元素（包括示意图）- 支持多种节点类型
      else if (nodeType === 'image' || nodeType === 'img') {
        const titleInfo = checkForExistingTitle(editor, pos, node)
        console.log('titleInfo ==>', titleInfo)
        // 检查是否是示意图（带有 svgData 属性）
        const isSchematic = node.attrs && node.attrs.svgData
        const elementType = isSchematic ? 'schematic' : 'image'

        elements.push({
          type: elementType,
          category: 'figure',
          position: pos,
          node: node,
          hasExistingTitle: titleInfo.hasTitle,
          titleNode: titleInfo.titleNode
        })
      }

      return true // 继续遍历
    })

    // 按照在文档中的位置排序
    elements.sort((a, b) => a.position - b.position)

    return elements
  } catch (error) {
    console.error('识别图表和表格时出错:', error)
    return []
  }
}

/**
 * 为元素分配序号标签
 * @param elements 元素数组
 * @returns 处理后的元素数组
 */
export function assignSequentialLabels(elements: ChartElement[]): ChartElement[] {
  let figureCounter = 1
  let tableCounter = 1

  return elements.map((element) => {
    if (element.category === 'figure') {
      element.newLabel = `图${figureCounter}`
      figureCounter++
    } else if (element.category === 'table') {
      element.newLabel = `表${tableCounter}`
      tableCounter++
    }
    return element
  })
}

/**
 * 智能处理现有标题文本，避免重复标签
 * @param originalText 原始标题文本
 * @param newLabel 新的标签文本（如"图1"、"表2"）
 * @returns 处理后的标题文本
 */
function processExistingTitle(originalText: string, newLabel: string): string {
  if (!originalText || !newLabel) {
    return newLabel
  }

  // 去除原文本首尾空格
  const trimmedText = originalText.trim()

  // 添加调试信息
  console.log('processExistingTitle - 原始文本:', `"${trimmedText}"`)
  console.log('processExistingTitle - 新标签:', `"${newLabel}"`)

  // 更精确的正则表达式匹配现有的"图X"或"表X"前缀
  // 匹配 "图1 图1 图1" 这样的重复模式，或者单个 "图1"
  const figurePattern = /^(图\d+\s*)+/
  const tablePattern = /^(表\d+\s*)+/

  let result = ''

  if (figurePattern.test(trimmedText)) {
    // 替换所有重复的图编号为单个新编号
    result = trimmedText.replace(figurePattern, `${newLabel} `)
    console.log('processExistingTitle - 匹配图模式，结果:', `"${result}"`)
  } else if (tablePattern.test(trimmedText)) {
    // 替换所有重复的表编号为单个新编号
    result = trimmedText.replace(tablePattern, `${newLabel} `)
    console.log('processExistingTitle - 匹配表模式，结果:', `"${result}"`)
  } else {
    // 没有现有编号，直接添加
    result = `${newLabel} ${trimmedText}`
    console.log('processExistingTitle - 无匹配模式，结果:', `"${result}"`)
  }

  return result
}

/**
 * 创建居中对齐的标签段落
 * @param editor TipTap编辑器实例
 * @param labelText 标签文本
 * @returns 标签节点
 */
function createCenteredLabelNode(editor: Editor, labelText: string) {
  try {
    // 创建居中对齐的段落节点
    const labelNode = editor.schema.nodes.paragraph.create(
      { textAlign: 'center' }, // 设置居中对齐
      editor.schema.text(labelText, [
        editor.schema.marks.bold.create() // 加粗样式
      ])
    )
    return labelNode
  } catch (error) {
    // 如果不支持 textAlign 属性，则创建普通段落
    console.warn('不支持居中对齐，使用普通段落:', error)
    return editor.schema.nodes.paragraph.create(
      {},
      editor.schema.text(labelText, [editor.schema.marks.bold.create()])
    )
  }
}

/**
 * 更新现有标题文本，智能处理序号避免重复
 * @param editor TipTap编辑器实例
 * @param titleNode 标题节点
 * @param labelText 标签文本
 * @param titlePos 标题位置
 * @param tr 事务对象
 */
function updateExistingTitle(
  editor: Editor,
  titleNode: any,
  labelText: string,
  titlePos: number,
  tr: any
) {
  try {
    const originalText = titleNode.textContent.trim()
    // 使用智能标题处理函数，避免重复标签
    const newText = processExistingTitle(originalText, labelText)

    // 创建新的标题节点
    const newTitleNode = editor.schema.nodes.paragraph.create(
      { textAlign: 'center' }, // 居中对齐
      editor.schema.text(newText, [
        editor.schema.marks.bold.create() // 加粗样式
      ])
    )

    // 替换原有标题
    tr.replaceWith(titlePos, titlePos + titleNode.nodeSize, newTitleNode)
  } catch (error) {
    console.warn('更新标题时出错，使用普通格式:', error)
    const originalText = titleNode.textContent.trim()
    // 即使在错误处理中也使用智能标题处理
    const newText = processExistingTitle(originalText, labelText)

    const newTitleNode = editor.schema.nodes.paragraph.create(
      {},
      editor.schema.text(newText, [editor.schema.marks.bold.create()])
    )

    tr.replaceWith(titlePos, titlePos + titleNode.nodeSize, newTitleNode)
  }
}

/**
 * 将标签应用到编辑器中
 * @param editor TipTap编辑器实例
 * @param elements 带有新标签的元素数组
 * @returns 应用结果
 */
export function applyLabelsToEditor(editor: Editor, elements: ChartElement[]): boolean {
  if (!editor || !elements.length) {
    return false
  }

  try {
    // 开始事务
    const tr = editor.state.tr

    // 从后往前处理，避免位置偏移问题
    const sortedElements = [...elements].sort((a, b) => b.position - a.position)

    for (const element of sortedElements) {
      if (!element.newLabel) continue

      try {
        if (element.hasExistingTitle && element.titleNode) {
          // 如果有现有标题，更新标题文本
          const elementSize = element.node.nodeSize
          const titlePos = element.position + elementSize
          updateExistingTitle(editor, element.titleNode, element.newLabel, titlePos, tr)
        } else {
          // 如果没有标题，在元素下方创建新的居中标签
          const elementSize = element.node.nodeSize
          const insertPos = element.position + elementSize
          const labelNode = createCenteredLabelNode(editor, element.newLabel)
          tr.insert(insertPos, labelNode)
        }
      } catch (error) {
        console.warn(`为元素添加标签时出错:`, error)
        continue
      }
    }

    // 应用事务
    if (tr.docChanged) {
      editor.view.dispatch(tr)
      return true
    }

    return false
  } catch (error) {
    console.error('应用标签到编辑器时出错:', error)
    return false
  }
}

/**
 * 执行完整的图表排序流程
 * @param editor TipTap编辑器实例
 * @returns 排序结果
 */
export async function performChartSort(editor: Editor): Promise<ChartSortResult> {
  const result: ChartSortResult = {
    success: false,
    message: '',
    processedCount: 0,
    figureCount: 0,
    tableCount: 0,
    elements: []
  }

  try {
    // 步骤1：识别所有图表和表格元素
    const elements = identifyChartsAndTables(editor)

    if (elements.length === 0) {
      result.message = '未找到需要排序的图表或表格元素'
      return result
    }

    // 步骤2：分配序号标签
    const labeledElements = assignSequentialLabels(elements)

    // 步骤3：应用标签到编辑器
    const applied = applyLabelsToEditor(editor, labeledElements)

    if (applied) {
      // 统计结果
      const figureCount = labeledElements.filter((el) => el.category === 'figure').length
      const tableCount = labeledElements.filter((el) => el.category === 'table').length

      result.success = true
      result.message = `成功标注 ${figureCount} 个图和 ${tableCount} 个表`
      result.processedCount = labeledElements.length
      result.figureCount = figureCount
      result.tableCount = tableCount
      result.elements = labeledElements
    } else {
      result.message = '标注应用失败，请重试'
    }

    return result
  } catch (error) {
    console.error('执行图表排序时出错:', error)
    result.message = '图表排序过程中发生错误，请重试'
    return result
  }
}
