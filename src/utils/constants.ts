import { useWindowSize } from '@vueuse/core'
import { isXioin } from '@/utils/utils'

export const HTTP_STATUS = {
  SUCCESS: 200,
  CREATED: 201,
  ACCEPTED: 202,
  CLIENT_ERROR: 400,
  AUTHENTICATE: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504,
  SHORTAGE_OF_COINS: 7001,
  FAILED_PHONE_BINDING: 1001,
  FAILED_QRCODE_INVALID: 1002, //web扫码登录时二维码失效
  FAILED_SUBMIT_AGAIN: 20001, //接口重复提交
  MOBILE_NOT_BOUND: 7002 //手机号未绑定
}

export const isPcWeb = () => {
  const { width } = useWindowSize()
  return width.value > 960
}
//polish、expand、shorten、translate
export const ACTION_CODE = {
  POLISH: 'editor-polish', //,AI改写
  EXPAND: 'editor-expand', //AI扩写
  SHORTEN: 'editor-shorten', //AI缩写
  TRANSLATE: 'editor-translate', //AI翻译
  SIMPLE: 'editor-simple', //AI改写
  TABLE: 'editor-table', //AI表格
  CHART: 'editor-chart', //AI图表
  FORMULA: 'editor-formula', //AI公式
  IMAGE: 'editor-image', //AI画图
  SCHEMATIC: 'editor-schematic', //AI示意图
  REFERENCES_FORMAT: 'editor-format-references' //文献格式
}
export const ACTION_CODE_NAME = {
  POLISH: '改写',
  EXPAND: '扩写',
  SHORTEN: '缩写',
  TRANSLATE: '翻译'
}

export const ACTION_ERROR_CODE = {
  NORMAL: 0,
  TWO_FEW_WORDS: 1,
  TWO_MANY_WORDS: 2,
  SURE_COIN_BALANCE: 3,
  COIN_SHORTAGE: 4
}
const XIAOIN_MP_ID = 'wxab6895d97cd449c8' //公众号 行业圈 appId  万能小in（wxab6895d97cd449c8） 万能小in AI创作助手（wxd7025dc360a74a5d）
export const XIAO_WN_ID = 'wxd7025dc360a74a5d' // 公众号 AI创作助手  xiaoin.cn
export const getAppId = () => {
  // console.log('getAppId')
  // if (isH5() && isWechatBrowser()) {
  //   return isXioin ? XIAO_WN_ID : XIAOIN_MP_ID
  // }
  // if (!isMobileDevice() && !isWechatBrowser()) {
  //   return isXioin ? XIAO_WN_ID : XIAOIN_MP_ID
  // }
  return isXioin ? XIAO_WN_ID : XIAOIN_MP_ID
}
export const rewritingExpense = 10000 //改写费用

//团队成员角色
export enum TEAM_MEMBER_ROLE {
  OWNER = 'owner',
  ADMIN = 'admin',
  MEMBER = 'member'
}

export const KeyOfEventBus = {
  theRechargeIsSuccessful: 'theRechargeIsSuccessful', //充值成功
  isVisibleRecharge: 'isVisibleRecharge', // 资讯页面最下方 升级助手 按钮点击传值打开充值弹窗
  askAiQuestionInTheFrontPageOfTheKnolwedgeBase:
    'ask_ai_question_in_the_front_page_of_the_knolwedge_base',
  openKnolwedgeBasePage: 'open_knolwedge_base_page',
  updateTheSubmissionAttachmentInformation: 'update_the_submission_attachment_information',
  createSubmissionSelectionPPTLecture: 'create_submission_selection_ppt_lecture', //选择PPT讲稿的eventbus 的key
  updateSubmissionInfo: 'update_submission_info', //用于确认订单文件读取完成后更新submission
  rechargeModalOpen: 'recharge_modal_open',
  goToLogin: 'go_to_login',
  updateLogin: 'update_login',
  updateMessageDelete: 'update_message_delete',
  beUpdateKnowledgeFilePage: 'be_update_knowledge_file_page', //在文件聊天时，点击文件名称更新左侧页码
  updateCreationSearchBoxKeyword: 'update_creation_search_box_keyword', //更新写作页的搜索框的值
  updateAiQuestionLeftMessageRecords: 'update_ai_question_left_message_records', //点击新搜索发送消息后，更新左侧列表
  highLightTranslate: 'high-light-translate', //点击翻译后的文本高亮
  updateAiQuestionLeftTitle: 'update_ai_question_left_title', //在搜索列表页更新标题后，同步到左侧列表中
  aiQuestionIsANewQuestionAndTheSessionIdThatIsBeingAnswered:
    'ai_question_is_a_new_question_and_the_session_id_that_is_being_answered', //AI提问是新搜索并且搜索正在回答中的session id
  isUploadTheAiQuestionResponseEnd: 'is_upload_the_ai_question_response_end', //是否更新聊天内容列表
  updateLeftMessageRecords: 'update_left_message_records', //更新搜索左侧菜单搜索记录
  updateLeftManualOperationMessageRecords: 'update_left_manual_operation_message_records', //更新搜索左侧菜单搜索记录，通过list手动换，不掉接口
  addToNotes: 'add_to_notes', // 添加到笔记
  chatScrollToBottom: 'chat_scroll_to_bottom', // 滚动到底部
  // updateMessageRefs: 'update_message_refs', //更新聊天返回refs
  // updateKnowledgeInfoOfCount: 'update_knowledge_info_of_count',
  // 创建书籍后确认支付弹窗
  bookEditorCreateAfterConfirmPayCancelModal: 'book_editor_create_after_confirm_pay_cancel_modal', // 文件上传后确认支付弹窗取消

  bookEditorCreateAfterConfirmPayModal: 'book_editor_create_after_confirm_pay_modal'
}
