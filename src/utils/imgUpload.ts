// import { getFileCosSign, uploadByUrl } from "@/api/upload"
// import { ElMessage } from "element-plus"
// import { getUploadCosSign, uploadImageCos } from "./utils"

// export const imgUpload = async (file: any) => {
//     //允许自行实现上传文件的请求
//     // var fd = new FormData()
//     // fd.append('file', file.file)
//     // fd.append('userName', uploadForm.userName)

//     // const { data } = await uloadFile(fd, (e) => {
//     //   //计算进度条的值e.loaded当前进度 ,e.total总进度
//     //   progressShu.value = Number(((e.loaded / e.total) * 100).toFixed(0))
//     // })
//     try {
//       // 文件需要上传
//       console.log(file, 'imgUpload')
//       const item = file
//       const result = await getFileCosSign({ filename: item.name })
//       // console.log('result ==>', result)
//       if (!result.ok || !result.data) {
//         ElMessage.error(item.name + '上传失败')
//         return undefined
//       }

//       const cosClientAndParams = await getUploadCosSign(result.data)
//       // console.log('cosClientAndParams ==>', cosClientAndParams)
//       const uploadResult = await uploadImageCos(
//         cosClientAndParams.cos,
//         undefined,
//         item,
//         cosClientAndParams.params,
//         () => {}
//       )
//       // console.log('uploadResult ==>', uploadResult)

//       const params = {
//         fileName: item.name,
//         fileUrl: `https://aimaster-1256600262.cos.ap-shanghai.myqcloud.com/${uploadResult}`
//       }
//       const uploadByUrlResult = await uploadByUrl(params)
//       if (!uploadByUrlResult.ok) {
//         ElMessage.error(item.name + '上传失败')
//         return
//       }
//       // console.log('uploadByUrlResult ==>', uploadByUrlResult)
//       const uploadRes = {
//         fileId: uploadByUrlResult.data.id,
//         fileUrl: uploadByUrlResult.data.fileUrl
//       }
//       return uploadRes.fileUrl
//     } catch (error) {
//       ElMessage.error('文件上传失败，请重试')
//       throw(error);
//     }
//   }
