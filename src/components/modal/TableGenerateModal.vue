<template>
  <div>
    <el-dialog
      v-model="visible"
      title="AI生成表格"
      width="800px"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      center
    >
      <el-form layout="vertical">
        <div class="reference-text" v-if="modalStore.referceText">
          <div class="text-ellipsis-wrapper">
            <div class="text-ellipsis">
              {{ modalStore.referceText }}
            </div>
          </div>
        </div>

        <el-form-item required>
          <el-input
            v-model="prompt"
            :rows="8"
            placeholder="请输入你想要生成的表格内容或补充要求"
            type="textarea"
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel">取 消</el-button>
          <el-button type="primary" @click="handleOk" :loading="loading">生 成</el-button>
        </div>
      </template>
    </el-dialog>

    <PayModal
      v-if="modalStore.payModalVisible"
      v-model:model-value="modalStore.payModalVisible"
      @recharge="onRecharge"
      @confirm="onConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ACTION_CODE } from '@/utils/constants'

import { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue'
import { UserService } from '@/services/user'

import { generateContent } from '@/api/user'

import { useModalStore } from '../../stores/modalStore'
import PayModal from '@/components/modal/PayModal.vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { safeJsonParse } from '@/utils/utils'

const modalStore = useModalStore()
const userStore = useUserStore()

const props = defineProps<{
  modelValue: boolean
  vipLevel?: number
  submissionId: string
  title?: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', content: {}): void
  (e: 'recharge', code: string, value: number): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => {
    emit('update:modelValue', val)
  }
})

const prompt = ref('')
const loading = ref(false)

watch(
  () => visible.value,
  (val) => {
    if (!val) {
      prompt.value = ''
    }
  }
)

const sumbitData = async () => {
  if (!props.submissionId) {
    ElMessage.error('表格生成错误')
    return
  }
  loading.value = true

  try {
    let userContext = ''
    if (modalStore.referceText) {
      userContext = `${modalStore.referceText}, \n 请结合上下文生成表格`
    }
    const params: any = {
      submissionId: props.submissionId,
      code: ACTION_CODE.TABLE,
      content: userContext || prompt.value,
      params: {
        title: props.title,
        ask: prompt.value
      }
    }
    if (userStore.getTeamId) {
      params.teamId = userStore.getTeamId
    }
    const response = await generateContent(params)
    // console.log(' response ==>', response)
    if (response.ok && response.code == 200) {
      const jsonString = response.data.replace(/```json\n|\n```/g, '')
      const parsedResult: any = safeJsonParse(jsonString)

      emit('confirm', parsedResult)
      clearContent()
      await UserService.loadUserInfoAndAssistantMemberInfo()
    }
  } catch (error) {
    console.error('生成表格失败:', error)

    ElMessage.error('生成失败，请重试')
  } finally {
    loading.value = false
  }
}

const handleOk = async () => {
  if (!modalStore.referceText && !prompt.value) {
    ElMessage.warning('请选择参考文本或输入内容')
    return
  }
  // 打开支付弹窗
  modalStore.openPayModal()
}

const handleCancel = () => {
  if (loading.value) {
    ElMessage.warning('正在创作中，请勿关闭弹窗')
    return
  }
  clearContent()
}

const onRecharge = () => {
  emit('recharge', ACTION_CODE.CHART, 1)
}

const onConfirm = () => {
  sumbitData()
}

const clearContent = () => {
  modalStore.closeAllModals()
  modalStore.closeReferceText()
  // emit('update:modelValue', false)
  visible.value = false
  prompt.value = ''
}

onMounted(() => {})

onBeforeUnmount(() => {
  clearContent()
})
</script>

<style lang="scss" scoped>
.form-container {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.reference-text {
  margin: 1.25rem 0;
  padding: 0.75rem 1.25rem;
  background-color: #ebf5ff;
  border-radius: 0.5rem;
  color: #4a5568;
}

.text-ellipsis-wrapper {
  position: relative;
  overflow: hidden;
  height: 3em;
}
</style>
