<template>
  <a-modal
    v-model:open="localVisible"
    :width="500"
    :centered="true"
    :destroyOnClose="true"
    @cancel="handleCancel"
    class="exchange-search-modal"
  >
    <div class="exchange-content">
      <!-- 选择区域 -->
      <div class="mb-6">
        <div class="text-[18px] text-center text-[#333333] mb-5 font-bold">兑换学术搜索次数</div>
        <div class="flex gap-3 mb-4">
          <a-radio-group v-model:value="selectedOptionIndex" class="exchange-radio-group flex-1">
            <a-radio-button :value="10">10次</a-radio-button>
            <a-radio-button :value="50">50次</a-radio-button>
            <a-radio-button :value="100">100次</a-radio-button>
            <a-radio-button :value="'custom'">自定义</a-radio-button>
          </a-radio-group>

          <!-- 自定义输入框 -->
          <a-input-number
            v-if="selectedOptionIndex === 'custom'"
            v-model:value="customTimes"
            :min="1"
            :max="99999999"
            :step="1"
            placeholder="请输入次数"
            class="w-[100px]"
            @change="handleCustomTimesChange"
          />
        </div>
      </div>

      <!-- 信息显示区域 -->
      <div class="bg-[#F7F7F7] rounded-lg p-4 space-y-3">
        <div class="flex justify-between items-center">
          <span class="text-sm text-[#777777]">消耗：</span>
          <span class="text-sm font-medium text-[#2551B5]"
            >{{ formatCoins(requiredCoins) }}硬币</span
          >
        </div>
      </div>
      <div class="bg-[#F7F7F7] rounded-lg p-4 space-y-3 mt-[10px]">
        <div class="flex justify-between items-center">
          <span class="flex items-center text-sm text-[#777777]">
            <img
              class="w-[17px] h-[17px] mr-[5px]"
              src="https://static-1256600262.file.myqcloud.com/xiaoin-h5/icons/new-pc/yingbi.png"
            />
            剩余硬币：
          </span>
          <span
            class="text-sm font-medium"
            :class="isInsufficientCoins ? 'text-red-600' : 'text-[#333333]'"
          >
            {{ formatCoins(userCoinBalance) }}
          </span>
        </div>
        <div v-if="isInsufficientCoins" class="text-xs text-red-600 bg-red-50 p-2 rounded">
          硬币余额不足，请先<a class="text-[#2551B5]" @click="handleRecharge">充值</a>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end space-x-3">
        <a-button @click="handleCancel" :disabled="isExchanging"> 取消 </a-button>
        <a-button
          type="primary"
          @click="handleConfirm"
          :loading="isExchanging"
          :disabled="isInsufficientCoins || selectedTimes <= 0"
          style="color: #ffffff"
        >
          确认
        </a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { computed, onUnmounted, watch } from 'vue'
// import { useExchangeSearchStore } from '~/stores/exchangeSearch';

const props = defineProps<{
  visible: boolean
}>()

const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

const exchangeStore = useExchangeSearchStore()
const {
  selectedOptionIndex,
  customTimes,
  isExchanging,
  selectedTimes,
  requiredCoins,
  userCoinBalance,
  isInsufficientCoins
} = storeToRefs(exchangeStore)

// 本地可见性状态，用于双向绑定
const localVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 防止背景滚动
const preventBodyScroll = () => {
  document.body.style.overflow = 'hidden'
  document.body.style.paddingRight = '0px'
}

const restoreBodyScroll = () => {
  document.body.style.overflow = ''
  document.body.style.paddingRight = ''
}

const handleRecharge = () => {
  //   const rechargeStore = useRechargeStore()
  //   rechargeStore.openRechargeModal(RechargeModalTab.coin)
}

// 监听外部visible变化，同步到store
watch(
  () => props.visible,
  (newValue) => {
    if (newValue) {
      exchangeStore.openExchangeModal()
      preventBodyScroll()
    } else {
      exchangeStore.closeExchangeModal()
      restoreBodyScroll()
    }
  }
)

// 组件卸载时恢复滚动
onUnmounted(() => {
  restoreBodyScroll()
})

// 处理自定义次数变化
const handleCustomTimesChange = (value: string | number | null) => {
  if (value !== null && typeof value === 'number') {
    exchangeStore.setCustomTimes(value)
    // 当用户输入自定义次数时，自动选中自定义选项
    if (selectedOptionIndex.value !== 'custom') {
      exchangeStore.selectOption('custom')
    }
  }
}

// 格式化硬币数量显示
const formatCoins = (coins: number): string => {
  if (coins >= 10000) {
    return `${(coins / 10000).toFixed(1)}万`
  }
  return coins.toString()
}

// 取消操作
const handleCancel = () => {
  emit('update:visible', false)
}

// 确认兑换
const handleConfirm = async () => {
  await exchangeStore.performExchange()
  if (!exchangeStore.isExchanging) {
    emit('update:visible', false)
  }
}
</script>

<style scoped>
.exchange-search-modal :deep(.ant-modal-body) {
  padding: 24px;
}

.exchange-search-modal :deep(.ant-modal-footer) {
  padding: 16px 24px;
  border-top: 1px solid #f0f0f0;
}

/* 自定义 radio 组件样式 */
.exchange-radio-group :deep(.ant-radio-button-wrapper) {
  border-color: #e5e5e5;
  color: #333333;
  transition: all 0.3s;
  padding: 4px 16px;
  font-size: 14px;
  font-weight: 500;
  height: auto;
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.exchange-radio-group :deep(.ant-radio-button-wrapper:hover) {
  border-color: #2551b5;
}

.exchange-radio-group :deep(.ant-radio-button-wrapper-checked) {
  background-color: #e7edfe !important;
  border-color: #2551b5 !important;
  color: #2551b5 !important;
}

.exchange-radio-group :deep(.ant-radio-button-wrapper-checked:hover) {
  background-color: #e7edfe !important;
  border-color: #2551b5 !important;
  color: #2551b5 !important;
}

.exchange-radio-group :deep(.ant-radio-button-wrapper-checked::before) {
  background-color: #2551b5 !important;
}

/* 确保水平一行显示并平均分布 */
.exchange-radio-group {
  display: flex;
  flex-wrap: nowrap;
  width: 100%;
}

.exchange-radio-group :deep(.ant-radio-button-wrapper) {
  flex: 1;
  text-align: center;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .exchange-search-modal :deep(.ant-modal) {
    margin: 16px;
    max-width: calc(100vw - 32px);
  }

  .exchange-search-modal :deep(.ant-modal-body) {
    padding: 16px;
  }

  .exchange-search-modal :deep(.ant-modal-footer) {
    padding: 12px 16px;
  }

  /* 移动端保持一行布局，但调整按钮大小 */
  .exchange-radio-group :deep(.ant-radio-button-wrapper) {
    padding: 6px 8px;
    font-size: 13px;
    min-height: 36px;
  }

  /* 移动端容器可以换行显示输入框 */
  .flex.gap-3.mb-4 {
    flex-wrap: wrap;
  }
}

/* 输入框样式与选择项保持一致 */
.w-\[100px\] :deep(.ant-input-number) {
  height: 40px;
  border-color: #e5e5e5;
  transition: all 0.3s;
}

.w-\[100px\] :deep(.ant-input-number:hover) {
  border-color: #2551b5;
}

.w-\[100px\] :deep(.ant-input-number-input) {
  color: #333333;
  font-size: 14px;
  font-weight: 500;
  height: 38px;
  line-height: 38px;
}
</style>
