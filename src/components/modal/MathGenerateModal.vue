<template>
  <div>
    <el-dialog
      v-model="visible"
      title="AI生成数学公式"
      width="800px"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      center
    >
      <el-form layout="vertical">
        <!--   -->
        <div class="reference-text" v-if="modalStore.referceText">
          <div class="text-ellipsis-wrapper">
            <div class="text-ellipsis">
              {{ modalStore.referceText }}
            </div>
          </div>
        </div>
        <el-form-item required>
          <el-input
            v-model="prompt"
            :rows="8"
            placeholder="请输入你想要生成的公式内容或补充要求"
            type="textarea"
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel">取 消</el-button>
          <el-button type="primary" @click="handleOk" :loading="loading">生 成</el-button>
        </div>
      </template>
    </el-dialog>

    <PayModal
      v-if="modalStore.payModalVisible"
      v-model:model-value="modalStore.payModalVisible"
      @recharge="onRecharge"
      @confirm="onConfirm"
    />
  </div>
</template>

<script setup lang="ts">
// import { useChapterStore } from '@/stores/chapter'
import { ACTION_CODE } from '@/utils/constants'

import { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue'
import { UserService } from '@/services/user'

import { generateContent } from '@/api/user'
import { useModalStore } from '../../stores/modalStore'
import PayModal from '@/components/modal/PayModal.vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { isJSON } from '@/utils/utils'

const modalStore = useModalStore()
const userStore = useUserStore()

const props = defineProps<{
  modelValue: boolean
  vipLevel?: number
  submissionId: string
  title?: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', value: { content: any }): void
  (e: 'recharge', code: string, value: number): void
}>()

const loading = ref(false)
const prompt = ref('')

const visible = computed({
  get: () => props.modelValue,
  set: (val) => {
    emit('update:modelValue', val)
  }
})

const generateFormulaContent = async (): Promise<any> => {
  if (!props.submissionId) {
    ElMessage.error('公式生成错误')
    return
  }
  loading.value = true
  try {
    let userContext = ''
    if (modalStore.referceText) {
      userContext = `${modalStore.referceText}, \n 请结合上下文生成公式`
    }
    const params: any = {
      submissionId: props.submissionId,
      code: ACTION_CODE.FORMULA,
      content: userContext || prompt.value,
      params: {
        title: props.title,
        ask: prompt.value
      }
    }
    if (userStore.getTeamId) {
      params.teamId = userStore.getTeamId
    }
    const response = await generateContent(params)
    // console.log('response ==>', response)
    if (response.ok && response.code == 200) {
      if (isJSON(response.data)) {
        return {
          content: JSON.parse(response.data)
        }
      }
      ElMessage.error('公式插入失败')
      return { content: [] }
    } else {
      ElMessage.error('生成公式失败')
      return { content: [] }
    }
  } catch (error) {
    console.error('生成公式失败:', error)

    ElMessage.error('生成失败，请重试')
    return { content: [] }
  } finally {
    loading.value = false
  }
}
const sumbitData = async () => {
  const result = await generateFormulaContent()
  if (!result.content) return

  emit('confirm', result.content)

  await UserService.loadUserInfoAndAssistantMemberInfo()
  clearContent()
}

const onRecharge = () => {
  emit('recharge', ACTION_CODE.FORMULA, 1)
}

const onConfirm = () => {
  sumbitData()
}

const handleOk = async () => {
  if (!modalStore.referceText && !prompt.value) {
    ElMessage.warning('请选择参考文本或输入内容')
    return
  }
  // 打开支付弹窗
  modalStore.openPayModal()
}

const handleCancel = () => {
  if (loading.value) {
    ElMessage.warning('正在创作中，请勿关闭弹窗')
    return
  }
  clearContent()
}

const clearContent = () => {
  modalStore.closeAllModals()
  modalStore.closeReferceText()
  // emit('update:modelValue', false)
  visible.value = false
  prompt.value = ''
}

// 监听 modelValue 变化，当弹窗打开时重置表单
watch(
  () => visible.value,
  (newValue) => {
    if (newValue) {
      prompt.value = ''
    }
  }
)

onMounted(() => {})

onBeforeUnmount(() => {
  clearContent()
})
</script>

<style lang="scss" scoped>
.reference-text {
  margin: 1.25rem 0;
  padding: 0.75rem 1.25rem;
  background-color: #ebf5ff;
  border-radius: 0.5rem;
  color: #4a5568;
}

.preview-section {
  margin-top: 16px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.preview-title {
  font-weight: bold;
  margin-bottom: 8px;
}

.text-ellipsis-wrapper {
  position: relative;
  overflow: hidden;
  height: 3em;
}

.preview-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60px;
  padding: 16px;
  background-color: white;
  border-radius: 4px;
}
</style>
